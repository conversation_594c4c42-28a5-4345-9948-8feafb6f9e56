const fs = require('fs');

try {
    const content = fs.readFileSync('umi (1).js', 'utf8');
    const lines = content.split('\n');
    
    console.log('=== Trojan edit button context around line 105218 ===');
    for(let i = 105210; i <= 105230; i++) {
        console.log(`${i}: ${lines[i-1] || ''}`);
    }
    
    console.log('\n=== Context around line 105540 ===');
    for(let i = 105535; i <= 105555; i++) {
        console.log(`${i}: ${lines[i-1] || ''}`);
    }
    
    console.log('\n=== Search for component C definition in larger range ===');
    for(let i = 0; i < 5000; i++) {
        const line = lines[i] || '';
        if (line.includes('C["a"]') || line.includes('C = ') || line.includes('var C =') || 
            line.includes('C=') || line.includes('import') && line.includes('C')) {
            console.log(`${i+1}: ${line}`);
        }
    }
    
    console.log('\n=== Search for trojan form/modal component ===');
    for(let i = 110000; i < 115000; i++) {
        const line = lines[i] || '';
        if (line.includes('trojan') || line.includes('Trojan') || line.includes('serverTrojan')) {
            console.log(`${i+1}: ${line}`);
        }
    }
    
} catch (error) {
    console.error('Error:', error.message);
} 